# Telegram Bot 需求文档

## 项目概述

基于 Telegram 的频道管理和词条查询机器人，主要功能包括频道收藏、自动推送、词条管理等。参考项目：t.me/gohome

## 功能模块

### 1. 用户管理模块

#### 1.1 用户收藏频道管理
- **功能描述**：用户可以管理自己收藏的频道
- **具体需求**：
  - 查看收藏的频道列表
  - 取消收藏功能
  - 收藏频道数量统计

#### 1.2 用户词条提交与管理
- **功能描述**：用户可以提交和管理词条
- **具体需求**：
  - 用户提交新词条
  - 修改已提交的词条
  - 查看提交历史

### 2. 频道管理模块

#### 2.1 频道收藏功能
- **功能描述**：用户通过发送链接收藏频道
- **操作流程**：
  1. 用户点击"收藏频道"按钮
  2. Bot 提示用户发送频道链接
  3. 验证链接有效性
  4. 保存到用户收藏列表

#### 2.2 频道找回功能
- **功能描述**：自动检测失效频道并提供更新机制
- **具体需求**：
  - 定期检查收藏频道链接有效性
  - 链接失效时通知用户
  - 提供更新新链接的功能

#### 2.3 频道绑定功能
- **功能描述**：Bot 加入频道后自动生成推广链接
- **操作流程**：
  1. 用户邀请 Bot 加入频道并设为管理员
  2. Bot 自动生成该频道的推广链接
  3. 通过推广链接进入的用户自动收藏对应频道

### 3. 推送与通知模块

#### 3.1 自动推送功能
- **功能描述**：管理员设定内容自动推送到频道
- **具体需求**：
  - 管理员设定推送内容
  - 选择推送频率（定时推送）
  - 内容在频道中保持置顶
  - 推送到 Bot 管理的频道中

#### 3.2 频道大全展示
- **功能描述**：汇总展示所有用户收藏的频道
- **具体需求**：
  - 公共频道列表展示
  - 按分类或热度排序
  - 提供搜索功能

### 4. 词条管理模块

#### 4.1 词条数据结构
- **词条名称**：关键词（如"哈吉米"）
- **简介**：词条的详细说明
- **相关信息**：相关链接或补充内容
- **存储规则**：同名词条覆盖更新

#### 4.2 词条提交与审核
- **功能描述**：用户提交词条，管理员审核
- **操作流程**：
  1. 用户提交新词条（包含名称、简介、相关信息）
  2. 管理员审核词条内容
  3. 审核通过后公开显示

#### 4.3 词条查询与展示
- **功能描述**：用户查询词条信息
- **具体需求**：
  - 用户发送词条名称给 Bot
  - Bot 返回对应的词条信息
  - 支持模糊搜索
  - 提供相关词条推荐

### 5. 数据存储与管理

#### 5.1 数据库设计
- **用户表**：用户ID、收藏频道列表、提交词条记录
- **频道表**：频道ID、频道名称、链接、收藏次数、状态
- **词条表**：词条名称、简介、相关信息、提交者、审核状态
- **推送表**：推送内容、频率设置、目标频道

#### 5.2 数据备份与恢复
- **功能描述**：定期备份数据，防止数据丢失
- **具体需求**：
  - 自动定期备份
  - 手动备份功能
  - 数据恢复机制

## 技术要求

### 开发环境
- 编程语言：Python
- 框架：python-telegram-bot 或类似
- 数据库：SQLite 或 PostgreSQL
- 部署：支持服务器部署

### 性能要求
- 支持并发用户访问
- 响应时间 < 3秒
- 数据一致性保证

## 开发计划

1. **第一阶段**：基础框架搭建和数据库设计
2. **第二阶段**：用户管理和频道收藏功能
3. **第三阶段**：频道找回和绑定功能
4. **第四阶段**：自动推送和频道大全
5. **第五阶段**：词条管理功能
6. **第六阶段**：测试和部署
7. **第七阶段**：用户反馈收集和优化

## 参考信息

- 参考项目：t.me/gohome（基础功能参考，但缺少频道收藏功能）
- 目标：在参考项目基础上增加频道收藏和管理功能

## 备注

- 词条管理中同名词条采用覆盖更新策略
- 推送内容需要在频道中保持置顶状态
- 频道绑定功能需要 Bot 具有管理员权限
